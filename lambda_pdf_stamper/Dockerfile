FROM public.ecr.aws/lambda/python:3.11

# Instalar dependencias del sistema necesarias
RUN yum update -y && \
    yum install -y \
    gcc \
    gcc-c++ \
    make \
    && yum clean all

# Copiar requirements y instalar dependencias de Python
COPY requirements.txt /var/task

# Instalar PyMuPDF usando una versión precompilada específica que funcione con ARM64
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir \
    PyMuPDF==1.23.26 \
    Pillow>=9.2.0 \
    boto3>=1.26.0 \
    requests>=2.28.0

# Copiar código de la aplicación
COPY lambda_function.py pdf_stamper.py /var/task/

# Configurar el handler
CMD ["lambda_function.lambda_handler"] 