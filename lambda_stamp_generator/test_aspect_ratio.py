#!/usr/bin/env python3
"""
Script de prueba para verificar que la estampilla mantiene la proporción 3.715
"""

import sys
import os
from stamp_generator import StampGenerator

def test_aspect_ratio():
    """Prueba que las estampillas mantengan la proporción 3.715"""
    
    generator = StampGenerator()
    
    # Casos de prueba con diferentes cantidades de contenido
    test_cases = [
        {
            "name": "Caso básico",
            "title": "DOCUMENTO OFICIAL",
            "properties": {
                "Número": "DOC-001",
                "Fecha": "2024-01-15",
                "Estado": "APROBADO"
            },
            "qr_content": "https://ejemplo.com/verificar/001"
        },
        {
            "name": "Caso con mucho texto",
            "title": "CERTIFICADO DE AUTENTICIDAD DIGITAL",
            "properties": {
                "Documento": "CERT-AUTENTICIDAD-DIGITAL-2024-001",
                "Fecha de emisión": "15 de enero de 2024",
                "Estado de verificación": "COMPLETAMENTE VERIFICADO",
                "Entidad emisora": "MINISTERIO DE TECNOLOGÍA",
                "Código de seguridad": "SEC-2024-ABCD-1234-EFGH"
            },
            "qr_content": "https://verificacion.gov.co/documento/CERT-AUTENTICIDAD-DIGITAL-2024-001"
        },
        {
            "name": "Caso mínimo",
            "title": "DOC",
            "properties": {
                "ID": "1"
            },
            "qr_content": "https://short.ly/1"
        }
    ]
    
    print("🧪 Probando mantenimiento de proporción 3.715...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        
        # Generar estampilla
        stamp_io = generator.generate_stamp(
            title=test_case["title"],
            properties=test_case["properties"],
            qr_content=test_case["qr_content"],
            logo_image=None,
            transparent_background=False
        )
        
        # Leer dimensiones de la imagen generada
        from PIL import Image
        stamp_io.seek(0)
        image = Image.open(stamp_io)
        width, height = image.size
        
        # Calcular proporción
        aspect_ratio = width / height
        expected_ratio = 3.715
        ratio_diff = abs(aspect_ratio - expected_ratio)
        
        # Mostrar resultados
        print(f"   📐 Dimensiones: {width} x {height} px")
        print(f"   📊 Proporción: {aspect_ratio:.3f}")
        print(f"   🎯 Esperada: {expected_ratio}")
        print(f"   📏 Diferencia: {ratio_diff:.6f}")
        
        # Verificar que la proporción sea correcta (tolerancia de 0.001)
        if ratio_diff < 0.001:
            print(f"   ✅ CORRECTO - Proporción mantenida")
        else:
            print(f"   ❌ ERROR - Proporción incorrecta")
            return False
        
        # Guardar imagen de prueba
        output_filename = f"test_stamp_{i}_{test_case['name'].lower().replace(' ', '_')}.png"
        with open(output_filename, 'wb') as f:
            stamp_io.seek(0)
            f.write(stamp_io.read())
        print(f"   💾 Guardado: {output_filename}")
    
    print("\n" + "=" * 60)
    print("🎉 ¡Todas las pruebas pasaron! La proporción 3.715 se mantiene correctamente.")
    return True

def test_minimum_dimensions():
    """Prueba que las dimensiones mínimas sean respetadas"""
    
    generator = StampGenerator()
    
    print("\n🔍 Probando dimensiones mínimas...")
    print("=" * 60)
    
    # Caso con contenido muy pequeño
    stamp_io = generator.generate_stamp(
        title="A",
        properties={"B": "C"},
        qr_content="D",
        logo_image=None,
        transparent_background=False
    )
    
    from PIL import Image
    stamp_io.seek(0)
    image = Image.open(stamp_io)
    width, height = image.size
    
    print(f"📐 Dimensiones mínimas: {width} x {height} px")
    print(f"📊 Proporción: {width/height:.3f}")
    
    # Verificar que las dimensiones sean razonables
    min_expected_width = 400  # Mínimo razonable
    min_expected_height = 100  # Mínimo razonable
    
    if width >= min_expected_width and height >= min_expected_height:
        print("✅ Dimensiones mínimas respetadas")
        return True
    else:
        print("❌ Dimensiones muy pequeñas")
        return False

if __name__ == "__main__":
    print("🚀 Iniciando pruebas de proporción de estampillas")
    
    try:
        # Ejecutar pruebas
        test1_passed = test_aspect_ratio()
        test2_passed = test_minimum_dimensions()
        
        if test1_passed and test2_passed:
            print("\n🎊 ¡TODAS LAS PRUEBAS EXITOSAS!")
            print("La lambda mantiene correctamente la proporción 3.715")
            sys.exit(0)
        else:
            print("\n💥 ALGUNAS PRUEBAS FALLARON")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 ERROR durante las pruebas: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
