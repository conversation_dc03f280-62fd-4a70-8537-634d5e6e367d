import json
import os
import tempfile
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
import io
import boto3
from botocore.config import Config
from botocore.exceptions import ClientError

# Importar la implementación real de StampGenerator
from stamp_generator import StampGenerator

class S3StampClient:
    def __init__(self):
        # Configurar path-style addressing para object storage S3-compatible
        config = Config(
            s3={
                'addressing_style': 'path'
            }
        )
        
        # Configuración igual que el proyecto de referencia
        self.s3_client = boto3.client(
            's3',
            endpoint_url=os.getenv('S3_ENDPOINT_URL'),
            aws_access_key_id=os.getenv('S3_ACCESS_KEY'),
            aws_secret_access_key=os.getenv('S3_SECRET_KEY'),
            region_name=os.getenv('S3_REGION', 'us-east-1'),
            config=config
        )
    
    def download_logo(self, bucket_name: str, logo_filename: str) -> Optional[str]:
        """
        Descarga logo desde S3: {bucket}/assets/{logo_filename}
        
        Args:
            bucket_name: Nombre del bucket
            logo_filename: Nombre del archivo del logo
            
        Returns:
            Path del archivo temporal o None si no existe
        """
        try:
            logo_assets_dir = os.getenv('LOGO_ASSETS_DIR', 'assets')
            s3_key = f"{logo_assets_dir}/{logo_filename}"
            
            # Crear archivo temporal
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
            temp_file.close()
            
            # Descargar desde S3
            self.s3_client.download_file(bucket_name, s3_key, temp_file.name)
            
            # Verificar que es una imagen válida
            try:
                from PIL import Image
                test_img = Image.open(temp_file.name)
                test_img.close()
            except Exception as img_error:
                os.unlink(temp_file.name)
                raise ValueError(f"El archivo descargado no es una imagen válida: {str(img_error)}")
            
            return temp_file.name
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchKey':
                print(f"Logo no encontrado en S3: {s3_key}")
                return None
            else:
                print(f"Error de cliente S3 al descargar logo: {e}")
                return None
        except Exception as e:
            print(f"Error inesperado al descargar logo: {e}")
            return None
    
    def upload_stamp(self, bucket_name: str, stamp_bytes: bytes, output_filename: str) -> str:
        """
        Sube estampilla a S3: {bucket}/radicados/{output_filename}
        
        Args:
            bucket_name: Nombre del bucket
            stamp_bytes: Bytes de la estampilla
            output_filename: Nombre del archivo de salida
            
        Returns:
            S3 key del archivo subido
        """
        try:
            stamp_output_dir = os.getenv('STAMP_OUTPUT_DIR', 'radicados')
            s3_key = f"{stamp_output_dir}/{output_filename}"
            
            # Crear archivo temporal
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
            temp_file.write(stamp_bytes)
            temp_file.close()
            
            # Subir a S3
            self.s3_client.upload_file(
                temp_file.name, 
                bucket_name, 
                s3_key,
                ExtraArgs={'ContentType': 'image/png'}
            )
            
            # Limpiar archivo temporal
            os.unlink(temp_file.name)
            
            return s3_key
            
        except ClientError as e:
            print(f"Error de cliente S3 al subir estampilla: {e}")
            raise ValueError(f"Error al subir estampilla a S3: {str(e)}")
        except Exception as e:
            print(f"Error inesperado al subir estampilla: {e}")
            raise ValueError(f"Error interno al subir estampilla: {str(e)}")
    
    def generate_presigned_url(self, bucket_name: str, s3_key: str, expiration_seconds: int) -> Optional[str]:
        """
        Genera URL prefirmada para descarga
        
        Args:
            bucket_name: Nombre del bucket
            s3_key: Clave del objeto en S3
            expiration_seconds: Tiempo de expiración en segundos
            
        Returns:
            URL prefirmada o None si hay error
        """
        try:
            if expiration_seconds <= 0:
                return None
                
            url = self.s3_client.generate_presigned_url(
                ClientMethod='get_object',
                Params={
                    'Bucket': bucket_name,
                    'Key': s3_key
                },
                ExpiresIn=expiration_seconds
            )
            
            return url
            
        except ClientError as e:
            print(f"Error de cliente S3 al generar URL prefirmada: {e}")
            return None
        except Exception as e:
            print(f"Error inesperado al generar URL prefirmada: {e}")
            return None

def authenticate_request(headers: Dict[str, str]) -> bool:
    """
    Valida el API key desde los headers
    
    Args:
        headers: Headers de la request
        
    Returns:
        True si la autenticación es exitosa
    """
    api_key = os.getenv('API_KEY', 'StampGenerator2024Key')  # Clave por defecto
    
    # Buscar el header x-api-key (case insensitive)
    request_api_key = None
    for key, value in headers.items():
        if key.lower() == 'x-api-key':
            request_api_key = value
            break
    
    if not request_api_key:
        return False
    
    return request_api_key == api_key

def parse_event(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parsea el evento para soportar tanto Lambda tradicional como Function URL
    Extrae bucket_name desde headers para mayor seguridad
    """
    # Detectar si es un evento de Function URL (HTTP request)
    if 'httpMethod' in event or 'requestContext' in event:
        # Es un evento de Function URL
        headers = event.get('headers', {})
        
        # Autenticar la request
        if not authenticate_request(headers):
            raise ValueError("API key inválido o faltante")
        
        # Extraer bucket_name desde headers
        bucket_name = None
        for key, value in headers.items():
            if key.lower() == 'x-bucket-name':
                bucket_name = value
                break
        
        if not bucket_name:
            raise ValueError("Header 'x-bucket-name' requerido")
        
        # Parsear body para otros parámetros
        parsed_data = {'bucket_name': bucket_name}
        
        try:
            if 'body' in event and event['body']:
                # El body puede venir como string, parsearlo
                if isinstance(event['body'], str):
                    body = json.loads(event['body'])
                else:
                    body = event['body']
                parsed_data.update(body)
            else:
                # Si no hay body, buscar en queryStringParameters
                query_params = event.get('queryStringParameters', {})
                if query_params:
                    parsed_data.update(query_params)
        except json.JSONDecodeError:
            raise ValueError("Body JSON inválido")
            
        return parsed_data
    else:
        # Es un evento de Lambda tradicional
        # Para backward compatibility, usar API key desde event si está presente
        if 'api_key' in event:
            api_key = os.getenv('API_KEY', 'StampGenerator2024Key')
            if event['api_key'] != api_key:
                raise ValueError("API key inválido")
        
        return event

def validate_filename(filename: str, file_type: str) -> None:
    """
    Valida que el nombre de archivo tenga la extensión correcta
    
    Args:
        filename: Nombre del archivo
        file_type: Tipo de archivo ('logo' o 'output')
    """
    if not filename.lower().endswith('.png'):
        raise ValueError(f"El {file_type} debe tener extensión .png")

def generate_stamp_service(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Servicio principal para generar estampillas y subirlas a S3
    
    Args:
        request_data: Datos de la request parseados
        
    Returns:
        Dict con la respuesta de éxito o error
    """
    # Validar parámetros requeridos
    required_params = ['title', 'properties', 'qr_content', 'bucket_name', 'output_filename']
    missing_params = [param for param in required_params if param not in request_data]
    
    if missing_params:
        raise ValueError(f'Parámetros faltantes: {", ".join(missing_params)}')
    
    # Extraer parámetros
    title = request_data['title']
    properties_list = request_data['properties']
    qr_content = request_data['qr_content']
    bucket_name = request_data['bucket_name']
    logo_filename = request_data.get('logo_filename')
    transparent_background = request_data.get('transparent_background', False)
    output_filename = request_data['output_filename']
    expiration_seconds = request_data.get('expiration_seconds', 3600)

    # Nuevos parámetros para dimensiones en puntos
    width_pt = request_data.get('width_pt')
    height_pt = request_data.get('height_pt')
    dimension_mode = request_data.get('dimension_mode', 'adjust')
    
    # Validaciones
    validate_filename(output_filename, 'archivo de salida')
    if logo_filename:
        validate_filename(logo_filename, 'logo')
    
    if not isinstance(expiration_seconds, int) or expiration_seconds < 0:
        raise ValueError("expiration_seconds debe ser un entero >= 0")

    # Validaciones para dimensiones en puntos
    if width_pt is not None or height_pt is not None:
        if width_pt is None or height_pt is None:
            raise ValueError("Si se especifica width_pt o height_pt, ambos parámetros son requeridos")

        if not isinstance(width_pt, (int, float)) or width_pt <= 0:
            raise ValueError("width_pt debe ser un número positivo")

        if not isinstance(height_pt, (int, float)) or height_pt <= 0:
            raise ValueError("height_pt debe ser un número positivo")

        if dimension_mode not in ['adjust', 'diagonal']:
            raise ValueError("dimension_mode debe ser 'adjust' o 'diagonal'")
    
    # Validar que properties sea una lista
    if not isinstance(properties_list, list):
        raise ValueError("'properties' debe ser una lista de objetos con 'label' y 'value'")
    
    # Convertir las propiedades de lista a diccionario
    try:
        properties_dict = {}
        for prop in properties_list:
            if not isinstance(prop, dict) or 'label' not in prop or 'value' not in prop:
                raise ValueError("Cada propiedad debe tener 'label' y 'value'")
            properties_dict[prop['label']] = prop['value']
    except Exception as e:
        raise ValueError(f"Error procesando propiedades: {str(e)}")
    
    # Inicializar cliente S3
    s3_client = S3StampClient()
    
    # Preparar el logo si se proporcionó
    logo_path = None
    temp_logo_file = None
    logo_found = False
    
    try:
        if logo_filename:
            logo_path = s3_client.download_logo(bucket_name, logo_filename)
            if logo_path:
                temp_logo_file = logo_path
                logo_found = True
            else:
                print(f"Logo {logo_filename} no encontrado, continuando sin logo")
        
        # Generar la estampilla usando la implementación real
        generator = StampGenerator()
        stamp_bytes_io = generator.generate_stamp(
            title,
            properties_dict,
            qr_content,
            logo_path,
            transparent_background,
            width_pt=width_pt,
            height_pt=height_pt,
            dimension_mode=dimension_mode
        )
        
        # Subir estampilla a S3
        s3_key = s3_client.upload_stamp(
            bucket_name,
            stamp_bytes_io.getvalue(),
            output_filename
        )
        
        # Generar URL prefirmada si se solicita
        presigned_url = None
        if expiration_seconds > 0:
            presigned_url = s3_client.generate_presigned_url(
                bucket_name,
                s3_key,
                expiration_seconds
            )
        
        # Obtener dimensiones finales de la estampilla generada
        from PIL import Image
        stamp_bytes_io.seek(0)
        temp_image = Image.open(stamp_bytes_io)
        final_width, final_height = temp_image.size
        stamp_bytes_io.seek(0)  # Resetear para uso posterior

        # Preparar respuesta
        response_data = {
            'filename': output_filename,
            's3_key': s3_key,
            'bucket': bucket_name,
            'content_type': 'image/png',
            'properties_processed': len(properties_dict),
            'has_logo': logo_filename is not None,
            'logo_found': logo_found,
            'transparent_background': transparent_background,
            'expiration_seconds': expiration_seconds,
            'dimensions': {
                'width_px': final_width,
                'height_px': final_height,
                'aspect_ratio': round(final_width / final_height, 3)
            }
        }

        # Agregar información de dimensiones especificadas si se usaron
        if width_pt is not None and height_pt is not None:
            response_data['input_dimensions'] = {
                'width_pt': width_pt,
                'height_pt': height_pt,
                'dimension_mode': dimension_mode
            }
        
        # Agregar URL prefirmada solo si se generó
        if presigned_url:
            response_data['presigned_url'] = presigned_url
        
        return response_data
        
    finally:
        # Limpiar el archivo temporal si se creó
        if temp_logo_file and os.path.exists(temp_logo_file):
            try:
                os.unlink(temp_logo_file)
            except Exception:
                pass  # Ignorar errores de limpieza

def lambda_handler(event: Dict[str, Any], context) -> Dict[str, Any]:
    """
    Handler principal que soporta tanto eventos Lambda como Function URL
    
    Para Function URL:
    Headers requeridos:
    - x-api-key: Clave de autenticación
    - x-bucket-name: Nombre del bucket S3
    
    Body:
    {
        "title": "Título de la estampilla",
        "properties": [
            {"label": "Etiqueta1", "value": "Valor1"},
            {"label": "Etiqueta2", "value": "Valor2"}
        ],
        "qr_content": "Contenido del código QR",
        "logo_filename": "logo.png",
        "transparent_background": false,
        "output_filename": "estampilla.png",
        "expiration_seconds": 3600,
        "width_pt": 200.0,
        "height_pt": 53.8,
        "dimension_mode": "adjust"
    }
    
    Para Lambda tradicional:
    {
        "title": "Título de la estampilla",
        "properties": [...],
        "qr_content": "...",
        "bucket_name": "nombre-del-bucket",
        "logo_filename": "logo.png",
        "transparent_background": false,
        "output_filename": "estampilla.png",
        "expiration_seconds": 3600,
        "api_key": "clave-opcional-para-validacion"
    }
    """
    try:
        # Parsear el evento (detecta automáticamente el formato y valida autenticación)
        parsed_event = parse_event(event)
        
        # Generar y subir la estampilla
        result = generate_stamp_service(parsed_event)
        
        success_response = {
            'statusCode': 200,
            'message': 'Estampilla generada y almacenada exitosamente',
            'data': result
        }
        
        # Formato de respuesta según el tipo de evento
        if 'httpMethod' in event or 'requestContext' in event:
            return {
                'statusCode': 200,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type, x-api-key, x-bucket-name'
                },
                'body': json.dumps(success_response)
            }
        else:
            return success_response
            
    except ValueError as e:
        # Errores de validación (autenticación, parámetros, etc.)
        error_response = {
            'error': str(e),
            'security_hint': 'Verifica headers: x-api-key, x-bucket-name',
            'required_params': ['title', 'properties', 'qr_content', 'output_filename']
        }
        
        status_code = 401 if 'API key' in str(e) else 400
        
        if 'httpMethod' in event or 'requestContext' in event:
            return {
                'statusCode': status_code,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps(error_response)
            }
        else:
            return {
                'statusCode': status_code,
                'body': json.dumps(error_response)
            }
            
    except Exception as e:
        print(f"Error en lambda_handler: {e}")
        error_response = {
            'error': f'Error interno del servidor: {str(e)}',
            'hint': 'Contacta al administrador si el problema persiste'
        }
        
        if 'httpMethod' in event or 'requestContext' in event:
            return {
                'statusCode': 500,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps(error_response)
            }
        else:
            return {
                'statusCode': 500,
                'body': json.dumps(error_response)
            }
