#!/usr/bin/env python3
"""
Script de debug para entender el problema con las dimensiones
"""

from stamp_generator import StampGenerator

def debug_dimensions():
    generator = StampGenerator()
    
    # Probar conversiones básicas
    print("=== CONVERSIONES BÁSICAS ===")
    width_pt, height_pt = 200.0, 53.8
    print(f"Input: {width_pt} x {height_pt} pt")
    
    # Modo adjust
    width_px_adj, height_px_adj = generator._calculate_dimensions_adjust_mode(width_pt, height_pt)
    print(f"Adjust mode: {width_px_adj} x {height_px_adj} px")
    print(f"Proporción adjust: {width_px_adj/height_px_adj:.6f}")
    
    # Modo diagonal
    width_px_diag, height_px_diag = generator._calculate_dimensions_from_diagonal(width_pt, height_pt)
    print(f"Diagonal mode: {width_px_diag} x {height_px_diag} px")
    print(f"Proporción diagonal: {width_px_diag/height_px_diag:.6f}")
    
    # Probar altura mínima
    print(f"\n=== ALTURA MÍNIMA ===")
    properties = {"Número": "TEST-001", "Fecha": "2024-01-15", "Estado": "VERIFICADO"}
    min_height = generator._calculate_minimum_height(len(properties))
    print(f"Altura mínima para {len(properties)} propiedades: {min_height} px")
    
    # Verificar si las dimensiones son suficientes
    print(f"\n=== VERIFICACIÓN ===")
    print(f"¿Altura adjust suficiente? {height_px_adj >= min_height} ({height_px_adj} >= {min_height})")
    print(f"¿Altura diagonal suficiente? {height_px_diag >= min_height} ({height_px_diag} >= {min_height})")
    
    # Simular el cálculo de ancho mínimo para contenido
    text_width = 200  # Estimación
    qr_size = 150
    margin_x = 15
    min_width_content = text_width + qr_size + (3 * margin_x)
    print(f"Ancho mínimo para contenido: {min_width_content} px")
    print(f"¿Ancho adjust suficiente? {width_px_adj >= min_width_content} ({width_px_adj} >= {min_width_content})")
    print(f"¿Ancho diagonal suficiente? {width_px_diag >= min_width_content} ({width_px_diag} >= {min_width_content})")

if __name__ == "__main__":
    debug_dimensions()
