#!/usr/bin/env python3
"""
Prueba simple para debug
"""

from stamp_generator import StampGenerator

def test_simple():
    generator = StampGenerator()
    
    print("Probando dimensiones específicas...")
    
    # Generar estampilla con dimensiones específicas
    stamp_io = generator.generate_stamp(
        title="TEST",
        properties={"ID": "1"},
        qr_content="test",
        logo_image=None,
        transparent_background=False,
        width_pt=200.0,
        height_pt=53.8,
        dimension_mode='adjust',
        force_dimensions=True
    )
    
    # Leer dimensiones
    from PIL import Image
    stamp_io.seek(0)
    image = Image.open(stamp_io)
    width_px, height_px = image.size
    
    print(f"Resultado: {width_px} x {height_px} px")
    print(f"Proporción: {width_px/height_px:.6f}")
    print(f"Esperada: 3.715")
    
    # Guardar para inspección
    with open("test_simple.png", 'wb') as f:
        stamp_io.seek(0)
        f.write(stamp_io.read())
    print("Guardado: test_simple.png")

if __name__ == "__main__":
    test_simple()
