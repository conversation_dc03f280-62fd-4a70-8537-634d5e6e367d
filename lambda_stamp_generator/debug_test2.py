#!/usr/bin/env python3
"""
Debug específico para el test 2
"""

from stamp_generator import StampGenerator

def debug_test2():
    generator = StampGenerator()
    
    print("=== DEBUG TEST 2 ===")
    width_pt, height_pt = 300.0, 80.7
    print(f"Input: {width_pt} x {height_pt} pt")
    
    # Probar conversión directa
    width_px_direct = generator._convert_pt_to_px(width_pt)
    height_px_direct = generator._convert_pt_to_px(height_pt)
    print(f"Conversión directa: {width_px_direct} x {height_px_direct} px")
    
    # Probar adjust mode
    width_px_adj, height_px_adj = generator._calculate_dimensions_adjust_mode(width_pt, height_pt)
    print(f"Adjust mode: {width_px_adj} x {height_px_adj} px")
    print(f"Proporción adjust: {width_px_adj/height_px_adj:.6f}")
    
    # Generar estampilla con force_dimensions=True
    stamp_io = generator.generate_stamp(
        title="DOC",
        properties={"ID": "002", "OK": "SI"},
        qr_content="test2",
        logo_image=None,
        transparent_background=False,
        width_pt=width_pt,
        height_pt=height_pt,
        dimension_mode='adjust',
        force_dimensions=True
    )
    
    # Leer dimensiones reales
    from PIL import Image
    stamp_io.seek(0)
    image = Image.open(stamp_io)
    width_px_real, height_px_real = image.size
    
    print(f"Resultado real: {width_px_real} x {height_px_real} px")
    print(f"Proporción real: {width_px_real/height_px_real:.6f}")
    
    # Verificar si coinciden
    print(f"¿Coincide con adjust? {width_px_real == width_px_adj and height_px_real == height_px_adj}")

if __name__ == "__main__":
    debug_test2()
