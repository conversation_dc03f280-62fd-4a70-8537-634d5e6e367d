#!/usr/bin/env python3
"""
Script de prueba completo para dimensiones en puntos, incluyendo casos con logos
"""

import sys
import os
import math
from stamp_generator import StampGenerator

def test_with_logo():
    """Prueba las dimensiones con logo incluido"""
    
    generator = StampGenerator()
    
    # Verificar que existe el logo predeterminado
    logo_path = "bw-icon.png"
    if not os.path.exists(logo_path):
        print(f"⚠️  Logo {logo_path} no encontrado, saltando pruebas con logo")
        return True
    
    test_cases = [
        {
            "name": "Con logo - estándar (300x80.7 pt)",
            "width_pt": 300.0,
            "height_pt": 80.7,
            "mode": "adjust",
            "force": False
        },
        {
            "name": "Con logo - forzado (200x53.8 pt)",
            "width_pt": 200.0,
            "height_pt": 53.8,
            "mode": "adjust",
            "force": True
        },
        {
            "name": "Con logo - diagonal (250x67.3 pt)",
            "width_pt": 250.0,
            "height_pt": 67.3,
            "mode": "diagonal",
            "force": False
        }
    ]
    
    print("🧪 Probando dimensiones con logo...")
    print("=" * 80)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print(f"   📏 Input: {test_case['width_pt']} x {test_case['height_pt']} pt")
        print(f"   🔧 Modo: {test_case['mode']}, Forzado: {test_case['force']}")
        
        # Generar estampilla con logo
        stamp_io = generator.generate_stamp(
            title="DOCUMENTO CON LOGO",
            properties={
                "Número": f"LOGO-{i:03d}",
                "Fecha": "2024-01-15",
                "Estado": "CERTIFICADO",
                "Entidad": "MINISTERIO DIGITAL"
            },
            qr_content=f"https://verify.gov.co/logo/{i}",
            logo_image=logo_path,
            transparent_background=False,
            width_pt=test_case["width_pt"],
            height_pt=test_case["height_pt"],
            dimension_mode=test_case["mode"],
            force_dimensions=test_case["force"]
        )
        
        # Leer dimensiones de la imagen generada
        from PIL import Image
        stamp_io.seek(0)
        image = Image.open(stamp_io)
        width_px, height_px = image.size
        
        # Calcular proporción
        aspect_ratio = width_px / height_px
        expected_ratio = 3.715
        ratio_diff = abs(aspect_ratio - expected_ratio)
        
        # Convertir píxeles de vuelta a puntos
        width_pt_result = width_px * 3/4
        height_pt_result = height_px * 3/4
        
        # Mostrar resultados
        print(f"   📐 Resultado: {width_px} x {height_px} px")
        print(f"   📐 En puntos: {width_pt_result:.1f} x {height_pt_result:.1f} pt")
        print(f"   📊 Proporción: {aspect_ratio:.3f}")
        print(f"   🎯 Esperada: {expected_ratio}")
        print(f"   📏 Diferencia: {ratio_diff:.6f}")
        
        # Verificar que la proporción sea correcta (tolerancia de 0.02)
        if ratio_diff < 0.02:
            print(f"   ✅ CORRECTO - Proporción mantenida")
        else:
            print(f"   ❌ ERROR - Proporción incorrecta")
            return False
        
        # Guardar imagen de prueba
        output_filename = f"test_logo_{i}_{test_case['mode']}_{'forced' if test_case['force'] else 'auto'}.png"
        with open(output_filename, 'wb') as f:
            stamp_io.seek(0)
            f.write(stamp_io.read())
        print(f"   💾 Guardado: {output_filename}")
    
    print("\n" + "=" * 80)
    print("🎉 ¡Todas las pruebas con logo pasaron!")
    return True

def test_edge_cases():
    """Prueba casos extremos"""
    
    generator = StampGenerator()
    
    test_cases = [
        {
            "name": "Dimensiones muy pequeñas (50x13.5 pt)",
            "width_pt": 50.0,
            "height_pt": 13.5,
            "mode": "adjust",
            "force": True,
            "expect_tiny": True
        },
        {
            "name": "Dimensiones muy grandes (800x215.4 pt)",
            "width_pt": 800.0,
            "height_pt": 215.4,
            "mode": "adjust",
            "force": False,
            "expect_tiny": False
        },
        {
            "name": "Proporción perfecta (371.5x100 pt)",
            "width_pt": 371.5,
            "height_pt": 100.0,
            "mode": "adjust",
            "force": True,
            "expect_tiny": False
        }
    ]
    
    print("\n🔍 Probando casos extremos...")
    print("=" * 80)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print(f"   📏 Input: {test_case['width_pt']} x {test_case['height_pt']} pt")
        
        try:
            # Generar estampilla
            stamp_io = generator.generate_stamp(
                title="CASO EXTREMO",
                properties={
                    "ID": f"EDGE-{i:03d}",
                    "Tipo": "Prueba"
                },
                qr_content=f"https://test.com/edge/{i}",
                logo_image=None,
                transparent_background=False,
                width_pt=test_case["width_pt"],
                height_pt=test_case["height_pt"],
                dimension_mode=test_case["mode"],
                force_dimensions=test_case["force"]
            )
            
            # Leer dimensiones
            from PIL import Image
            stamp_io.seek(0)
            image = Image.open(stamp_io)
            width_px, height_px = image.size
            
            aspect_ratio = width_px / height_px
            ratio_diff = abs(aspect_ratio - 3.715)
            
            print(f"   📐 Resultado: {width_px} x {height_px} px")
            print(f"   📊 Proporción: {aspect_ratio:.3f}")
            print(f"   📏 Diferencia: {ratio_diff:.6f}")
            
            # Verificar proporción
            if ratio_diff < 0.02:
                print(f"   ✅ CORRECTO - Proporción mantenida")
            else:
                print(f"   ⚠️  ADVERTENCIA - Proporción fuera de rango")
            
            # Guardar imagen
            output_filename = f"test_edge_{i}.png"
            with open(output_filename, 'wb') as f:
                stamp_io.seek(0)
                f.write(stamp_io.read())
            print(f"   💾 Guardado: {output_filename}")
            
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            return False
    
    print("\n" + "=" * 80)
    print("🎉 ¡Casos extremos completados!")
    return True

def test_api_compatibility():
    """Prueba compatibilidad con la API original"""
    
    generator = StampGenerator()
    
    print("\n🔄 Probando compatibilidad con API original...")
    print("=" * 80)
    
    # Generar estampilla sin especificar dimensiones (modo original)
    stamp_io = generator.generate_stamp(
        title="COMPATIBILIDAD API",
        properties={
            "Documento": "COMPAT-001",
            "Fecha": "2024-01-15",
            "Estado": "VÁLIDO"
        },
        qr_content="https://api.test.com/compat/001",
        logo_image=None,
        transparent_background=False
    )
    
    # Leer dimensiones
    from PIL import Image
    stamp_io.seek(0)
    image = Image.open(stamp_io)
    width_px, height_px = image.size
    
    aspect_ratio = width_px / height_px
    ratio_diff = abs(aspect_ratio - 3.715)
    
    print(f"   📐 Dimensiones automáticas: {width_px} x {height_px} px")
    print(f"   📊 Proporción: {aspect_ratio:.3f}")
    print(f"   📏 Diferencia: {ratio_diff:.6f}")
    
    # Guardar imagen
    with open("test_api_compat.png", 'wb') as f:
        stamp_io.seek(0)
        f.write(stamp_io.read())
    print(f"   💾 Guardado: test_api_compat.png")
    
    if ratio_diff < 0.02:
        print(f"   ✅ CORRECTO - API original mantiene proporción")
        return True
    else:
        print(f"   ❌ ERROR - API original no mantiene proporción")
        return False

if __name__ == "__main__":
    print("🚀 Iniciando pruebas completas de dimensiones en puntos")
    
    try:
        # Ejecutar todas las pruebas
        test1_passed = test_with_logo()
        test2_passed = test_edge_cases()
        test3_passed = test_api_compatibility()
        
        if test1_passed and test2_passed and test3_passed:
            print("\n🎊 ¡TODAS LAS PRUEBAS COMPLETAS EXITOSAS!")
            print("✅ Dimensiones en puntos funcionan correctamente")
            print("✅ Soporte para logos implementado")
            print("✅ Casos extremos manejados")
            print("✅ Compatibilidad con API original mantenida")
            sys.exit(0)
        else:
            print("\n💥 ALGUNAS PRUEBAS FALLARON")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 ERROR durante las pruebas: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
