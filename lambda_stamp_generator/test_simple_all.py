#!/usr/bin/env python3
"""
Test simplificado para verificar todas las funcionalidades
"""

from stamp_generator import StampGenerator

def test_all():
    generator = StampGenerator()
    
    test_cases = [
        {"name": "200x53.8 pt", "width_pt": 200.0, "height_pt": 53.8, "mode": "adjust"},
        {"name": "300x80.7 pt", "width_pt": 300.0, "height_pt": 80.7, "mode": "adjust"},
        {"name": "200x53.8 pt diagonal", "width_pt": 200.0, "height_pt": 53.8, "mode": "diagonal"},
        {"name": "150x100 pt adjust", "width_pt": 150.0, "height_pt": 100.0, "mode": "adjust"},
        {"name": "150x100 pt diagonal", "width_pt": 150.0, "height_pt": 100.0, "mode": "diagonal"}
    ]
    
    print("🧪 Test simplificado de dimensiones en puntos")
    print("=" * 60)
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        
        # Generar estampilla
        stamp_io = generator.generate_stamp(
            title="DOC",
            properties={"ID": f"{i:03d}", "OK": "SI"},
            qr_content=f"test{i}",
            logo_image=None,
            transparent_background=False,
            width_pt=test_case["width_pt"],
            height_pt=test_case["height_pt"],
            dimension_mode=test_case["mode"],
            force_dimensions=True
        )
        
        # Leer dimensiones
        from PIL import Image
        stamp_io.seek(0)
        image = Image.open(stamp_io)
        width_px, height_px = image.size
        
        aspect_ratio = width_px / height_px
        ratio_diff = abs(aspect_ratio - 3.715)
        
        print(f"   📐 Resultado: {width_px} x {height_px} px")
        print(f"   📊 Proporción: {aspect_ratio:.6f}")
        print(f"   📏 Diferencia: {ratio_diff:.6f}")
        
        if ratio_diff < 0.02:
            print(f"   ✅ CORRECTO")
        else:
            print(f"   ❌ ERROR")
            all_passed = False
        
        # Guardar
        with open(f"simple_test_{i}.png", 'wb') as f:
            stamp_io.seek(0)
            f.write(stamp_io.read())
    
    print(f"\n{'✅ TODAS LAS PRUEBAS PASARON' if all_passed else '❌ ALGUNAS PRUEBAS FALLARON'}")
    return all_passed

if __name__ == "__main__":
    test_all()
