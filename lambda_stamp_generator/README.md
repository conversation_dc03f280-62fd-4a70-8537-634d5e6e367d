# Lambda Stamp Generator con S3

Servicio AWS Lambda containerizado para generar estampillas documentales con códigos QR integrados y almacenamiento en Object Storage S3-compatible.

## 🎯 Características

- **Generación de Estampillas**: Crea estampillas PNG personalizadas con propiedades dinámicas
- **Códigos QR Integrados**: Incluye códigos QR con contenido personalizable y logo predeterminado
- **Logos desde S3**: Descarga logos desde Object Storage S3-compatible
- **Almacenamiento S3**: Guarda estampillas generadas en Object Storage
- **URLs Prefirmadas**: Genera URLs de descarga con tiempo de expiración configurable
- **Fondos Transparentes**: Opción para generar estampillas con fondo transparente
- **Autenticación Segura**: Protección mediante API Key en headers
- **Doble Compatibilidad**: Soporta eventos Lambda tradicionales y Function URL
- **Arquitectura ARM64**: Optimizado para mejor performance y menor costo
- **Path-Style Addressing**: Compatible con servicios S3-compatible que usan path-style

## 🏗️ Arquitectura

```
Cliente → Function URL → Lambda (Container ECR) → Object Storage S3-compatible → URL Prefirmada
```

**Componentes:**
- **ECR Repository**: `gedsys/gedsystool-stamp-generator`
- **Lambda Function**: `stamp-generator`
- **Function URL**: Endpoint público con autenticación por API Key
- **Object Storage**: Servicio S3-compatible con path-style addressing
- **IAM Role**: Permisos para CloudWatch Logs

## 📦 Estructura del Proyecto

```
lambda_stamp_generator/
├── lambda_function.py    # Handler principal con funcionalidad S3
├── stamp_generator.py    # Implementación completa del generador de estampillas
├── bw-icon.png          # Logo predeterminado para códigos QR
├── requirements.txt      # Dependencias Python con boto3
├── Dockerfile           # Contenedor ARM64 con todos los archivos
├── deploy.sh           # Script de despliegue automatizado
└── README.md           # Esta documentación
```

## 🚀 Despliegue

### Prerrequisitos

- AWS CLI configurado con permisos adecuados
- Docker instalado (preferiblemente con soporte ARM64)
- Permisos IAM: `ECRFullAccess`, `LambdaFullAccess`, `IAMFullAccess`
- Acceso a servicio Object Storage S3-compatible

### Despliegue Automatizado

```bash
# Hacer el script ejecutable (solo la primera vez)
chmod +x deploy.sh

# Ejecutar despliegue
./deploy.sh
```

### Configuración de Variables de Entorno S3

**IMPORTANTE**: Después del despliegue, configura las credenciales reales de tu servicio S3:

```bash
aws lambda update-function-configuration \
  --function-name stamp-generator \
  --environment "Variables={
    API_KEY=StampGenerator2024Key,
    S3_ENDPOINT_URL=https://tu-servicio-s3.com,
    S3_ACCESS_KEY=tu-access-key,
    S3_SECRET_KEY=tu-secret-key,
    S3_REGION=us-east-1,
    STAMP_OUTPUT_DIR=radicados,
    LOGO_ASSETS_DIR=assets
  }" \
  --region us-east-1
```

### Variables de Entorno

| Variable | Default | Descripción |
|----------|---------|-------------|
| `API_KEY` | `StampGenerator2024Key` | Clave de autenticación |
| `S3_ENDPOINT_URL` | - | URL del servicio S3-compatible |
| `S3_ACCESS_KEY` | - | Clave de acceso al Object Storage |
| `S3_SECRET_KEY` | - | Clave secreta del Object Storage |
| `S3_REGION` | `us-east-1` | Región del servicio |
| `STAMP_OUTPUT_DIR` | `radicados` | Directorio donde se guardan las estampillas |
| `LOGO_ASSETS_DIR` | `assets` | Directorio donde se buscan los logos |

## 📡 API Reference

### Endpoint
```
POST https://your-function-url.lambda-url.region.on.aws/
```

### Headers Requeridos
```
Content-Type: application/json
x-api-key: StampGenerator2024Key
x-bucket-name: nombre-del-bucket
```

### Request Body

```json
{
    "title": "Título de la estampilla",
    "properties": [
        {
            "label": "Documento",
            "value": "DOC-123456"
        },
        {
            "label": "Fecha",
            "value": "2024-01-15"
        },
        {
            "label": "Estado",
            "value": "APROBADO"
        }
    ],
    "qr_content": "https://ejemplo.com/verificar/123456",
    "logo_filename": "logo-empresa.png",
    "output_filename": "estampilla-doc123.png",
    "expiration_seconds": 3600,
    "transparent_background": false,
    "width_pt": 200.0,
    "height_pt": 53.8,
    "dimension_mode": "adjust"
}
```

### Parámetros

| Campo | Tipo | Requerido | Descripción |
|-------|------|-----------|-------------|
| `title` | string | ✅ | Título principal de la estampilla |
| `properties` | array | ✅ | Lista de propiedades con label y value |
| `qr_content` | string | ✅ | Contenido del código QR (URL o texto) |
| `output_filename` | string | ✅ | Nombre del archivo de salida (debe terminar en .png) |
| `logo_filename` | string | ❌ | Nombre del logo en `{bucket}/assets/` (debe terminar en .png) |
| `expiration_seconds` | integer | ❌ | Tiempo de vida de URL prefirmada (default: 3600, 0 = sin URL) |
| `transparent_background` | boolean | ❌ | Si el fondo debe ser transparente (default: false) |
| `width_pt` | float | ❌ | Ancho deseado en puntos (requiere height_pt) |
| `height_pt` | float | ❌ | Alto deseado en puntos (requiere width_pt) |
| `dimension_mode` | string | ❌ | Modo de cálculo: 'adjust' o 'diagonal' (default: 'adjust') |

### Response Exitosa (200)

```json
{
    "statusCode": 200,
    "message": "Estampilla generada y almacenada exitosamente",
    "data": {
        "filename": "estampilla-doc123.png",
        "s3_key": "radicados/estampilla-doc123.png",
        "bucket": "mi-bucket",
        "presigned_url": "https://tu-servicio-s3.com/mi-bucket/radicados/estampilla-doc123.png?...",
        "expiration_seconds": 3600,
        "content_type": "image/png",
        "properties_processed": 3,
        "has_logo": true,
        "logo_found": true,
        "transparent_background": false
    }
}
```

**Nota**: El campo `presigned_url` solo aparece si `expiration_seconds > 0`.

### Response de Error (400/401/500)

```json
{
    "error": "Descripción del error",
    "security_hint": "Verifica headers: x-api-key, x-bucket-name",
    "required_params": ["title", "properties", "qr_content", "output_filename"]
}
```

## 🧪 Ejemplos de Uso

### Con cURL - Estampilla con Logo y URL Prefirmada

```bash
# Obtener la Function URL
FUNCTION_URL=$(aws lambda get-function-url-config \
    --function-name stamp-generator \
    --region us-east-1 \
    --query FunctionUrl --output text)

# Generar estampilla con logo
curl -X POST "$FUNCTION_URL" \
    -H "Content-Type: application/json" \
    -H "x-api-key: StampGenerator2024Key" \
    -H "x-bucket-name: mi-bucket" \
    -d '{
        "title": "Documento Oficial",
        "properties": [
            {"label": "ID", "value": "DOC-001"},
            {"label": "Fecha", "value": "2024-01-15"},
            {"label": "Estado", "value": "CERTIFICADO"}
        ],
        "qr_content": "https://verificar.ejemplo.com/doc001",
        "logo_filename": "logo-empresa.png",
        "output_filename": "certificado-001.png",
        "expiration_seconds": 7200,
        "transparent_background": true
    }'
```

### Sin Logo y Sin URL Prefirmada

```bash
curl -X POST "$FUNCTION_URL" \
    -H "Content-Type: application/json" \
    -H "x-api-key: StampGenerator2024Key" \
    -H "x-bucket-name: mi-bucket" \
    -d '{
        "title": "Estampilla Simple",
        "properties": [
            {"label": "Número", "value": "EST-123"}
        ],
        "qr_content": "https://ejemplo.com/est123",
        "output_filename": "simple-123.png",
        "expiration_seconds": 0,
        "transparent_background": false
    }'
```

## 📁 Estructura de Archivos en S3

```
mi-bucket/
├── assets/                    # Directorio de logos (configurable)
│   ├── logo-empresa.png
│   ├── logo-gobierno.png
│   └── sello-oficial.png
└── radicados/                 # Directorio de estampillas (configurable)
    ├── certificado-001.png
    ├── simple-123.png
    └── documento-456.png
```

## 🔒 Seguridad

### Autenticación
- **API Key obligatorio**: Header `x-api-key` requerido en todas las requests
- **Bucket obligatorio**: Header `x-bucket-name` requerido
- **Validación case-insensitive**: Los headers se validan sin importar mayúsculas/minúsculas

### Validaciones
- **Extensiones de archivo**: Tanto logos como archivos de salida deben terminar en `.png`
- **Logos opcionales**: Si el logo no existe, continúa sin logo (`logo_found: false`)
- **Sobrescritura**: Los archivos existentes se sobrescriben automáticamente

### Headers de Seguridad
```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: POST, OPTIONS
Access-Control-Allow-Headers: Content-Type, x-api-key, x-bucket-name
```

## ⚡ Performance

### Configuración Optimizada
- **Arquitectura**: ARM64 (+34% price-performance vs x86_64)
- **Memoria**: 512MB (ajustable según volumen de logos)
- **Timeout**: 60 segundos (suficiente para operaciones S3)
- **Cold Start**: <3 segundos típico

### Límites Operacionales
- **Tamaño máximo de logo**: Limitado por el servicio S3
- **Propiedades**: Hasta 5 propiedades por estampilla
- **Formato de salida**: PNG con compresión óptima
- **Path-style**: Compatible con servicios S3 que no soportan virtual-hosted-style

## 🐛 Troubleshooting

### Error 401: "API key inválido o faltante"
```bash
# Verificar que el header esté presente
curl -v -H "x-api-key: StampGenerator2024Key" ...
```

### Error 400: "Header 'x-bucket-name' requerido"
```bash
# Verificar que el header del bucket esté presente
curl -v -H "x-bucket-name: mi-bucket" ...
```

### Error 400: "El archivo de salida debe tener extensión .png"
```bash
# Asegurar que output_filename termine en .png
"output_filename": "estampilla.png"
```

### Error: "Logo no encontrado"
- El logo se busca en `{bucket}/{LOGO_ASSETS_DIR}/{logo_filename}`
- Si no existe, continúa sin logo (`logo_found: false`)
- Verificar que el archivo exista en la ruta correcta

### Error 500: "Error al subir estampilla a S3"
```bash
# Verificar credenciales S3 en variables de entorno
aws lambda get-function-configuration --function-name stamp-generator
```

### Performance Issues
```bash
# Incrementar memoria si las operaciones S3 son lentas
aws lambda update-function-configuration \
    --function-name stamp-generator \
    --memory-size 768
```

## 🔧 Configuración Avanzada

### Cambiar Directorios S3

```bash
aws lambda update-function-configuration \
    --function-name stamp-generator \
    --environment "Variables={
        API_KEY=StampGenerator2024Key,
        S3_ENDPOINT_URL=https://tu-servicio-s3.com,
        S3_ACCESS_KEY=tu-access-key,
        S3_SECRET_KEY=tu-secret-key,
        S3_REGION=us-east-1,
        STAMP_OUTPUT_DIR=documentos,
        LOGO_ASSETS_DIR=imagenes
    }"
```

### Configurar Timeout Personalizado

```bash
aws lambda update-function-configuration \
    --function-name stamp-generator \
    --timeout 120
```

## 📊 Monitoreo

### Métricas Clave
- **Invocations**: Número de requests procesadas
- **Duration**: Tiempo de ejecución promedio (incluye operaciones S3)
- **Errors**: Tasa de errores (objetivo: <1%)
- **Throttles**: Limitaciones por concurrencia

### Logs Estructurados
```json
{
    "timestamp": "2024-01-15T10:30:00Z",
    "level": "INFO",
    "message": "Estampilla generada y almacenada",
    "s3_key": "radicados/documento-123.png",
    "bucket": "mi-bucket",
    "has_logo": true,
    "logo_found": true,
    "execution_time_ms": 1250
}
```

## 🔄 Actualizaciones

### Actualizar Solo el Código
```bash
# Re-ejecutar deploy manteniendo configuración
./deploy.sh
```

### Actualizar Credenciales S3
```bash
# Actualizar variables de entorno sin redesplegar
aws lambda update-function-configuration \
    --function-name stamp-generator \
    --environment "Variables={...nuevas-credenciales...}"
```

## 📞 Soporte

Para soporte técnico o reportar problemas:

1. Revisar logs en CloudWatch: `/aws/lambda/stamp-generator`
2. Verificar configuración de variables de entorno S3
3. Confirmar conectividad con el servicio Object Storage
4. Validar estructura de directorios en S3
5. Verificar permisos de lectura/escritura en el bucket

---

**Versión**: 2.0.0  
**Última actualización**: Enero 2024  
**Compatibilidad**: AWS Lambda Python 3.11, ARM64, Object Storage S3-compatible 