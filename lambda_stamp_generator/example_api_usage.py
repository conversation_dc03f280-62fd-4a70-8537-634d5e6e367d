#!/usr/bin/env python3
"""
Ejemplo de uso de la API con las nuevas funcionalidades de dimensiones en puntos
"""

import json
from lambda_function import lambda_handler

def test_api_with_pt_dimensions():
    """Prueba la API con dimensiones en puntos"""
    
    print("🚀 Ejemplo de uso de la API con dimensiones en puntos")
    print("=" * 70)
    
    # Ejemplo 1: Estampilla estándar con dimensiones específicas
    print("\n📋 Ejemplo 1: Estampilla estándar (200x53.8 pt)")
    
    event1 = {
        "httpMethod": "POST",
        "headers": {
            "x-api-key": "StampGenerator2024Key",
            "x-bucket-name": "test-bucket",
            "Content-Type": "application/json"
        },
        "body": json.dumps({
            "title": "DOCUMENTO OFICIAL",
            "properties": [
                {"label": "Número", "value": "DOC-2024-001"},
                {"label": "Fecha", "value": "15 de enero de 2024"},
                {"label": "Estado", "value": "APROBADO"}
            ],
            "qr_content": "https://verificacion.gov.co/doc/2024/001",
            "output_filename": "estampilla-001.png",
            "width_pt": 200.0,
            "height_pt": 53.8,
            "dimension_mode": "adjust",
            "force_dimensions": False
        })
    }
    
    try:
        # Simular la llamada (sin S3 real)
        print("   📤 Enviando request...")
        print("   📏 Dimensiones solicitadas: 200.0 x 53.8 pt")
        print("   🔧 Modo: adjust")
        print("   ⚙️  Force dimensions: False")
        print("   ✅ Request válido - dimensiones se ajustarán para contenido mínimo")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Ejemplo 2: Estampilla con modo diagonal
    print("\n📋 Ejemplo 2: Estampilla con cálculo diagonal (250x67.3 pt)")
    
    event2 = {
        "httpMethod": "POST", 
        "headers": {
            "x-api-key": "StampGenerator2024Key",
            "x-bucket-name": "test-bucket",
            "Content-Type": "application/json"
        },
        "body": json.dumps({
            "title": "CERTIFICADO DIGITAL",
            "properties": [
                {"label": "ID", "value": "CERT-2024-002"},
                {"label": "Tipo", "value": "Autenticidad"},
                {"label": "Válido hasta", "value": "31/12/2024"}
            ],
            "qr_content": "https://cert.gov.co/verify/002",
            "output_filename": "certificado-002.png",
            "width_pt": 250.0,
            "height_pt": 67.3,
            "dimension_mode": "diagonal",
            "force_dimensions": True,
            "transparent_background": True
        })
    }
    
    print("   📤 Enviando request...")
    print("   📏 Dimensiones solicitadas: 250.0 x 67.3 pt")
    print("   🔧 Modo: diagonal")
    print("   ⚙️  Force dimensions: True")
    print("   🎨 Fondo transparente: True")
    print("   ✅ Request válido - dimensiones exactas basadas en diagonal")
    
    # Ejemplo 3: Estampilla con logo
    print("\n📋 Ejemplo 3: Estampilla con logo (300x80.7 pt)")
    
    event3 = {
        "httpMethod": "POST",
        "headers": {
            "x-api-key": "StampGenerator2024Key", 
            "x-bucket-name": "test-bucket",
            "Content-Type": "application/json"
        },
        "body": json.dumps({
            "title": "DOCUMENTO INSTITUCIONAL",
            "properties": [
                {"label": "Expediente", "value": "EXP-2024-003"},
                {"label": "Dependencia", "value": "Secretaría Digital"},
                {"label": "Funcionario", "value": "Juan Pérez"},
                {"label": "Cargo", "value": "Director"},
                {"label": "Fecha", "value": "15/01/2024"}
            ],
            "qr_content": "https://institucional.gov.co/exp/003",
            "logo_filename": "logo-institucion.png",
            "output_filename": "documento-003.png",
            "width_pt": 300.0,
            "height_pt": 80.7,
            "dimension_mode": "adjust",
            "force_dimensions": False,
            "expiration_seconds": 7200
        })
    }
    
    print("   📤 Enviando request...")
    print("   📏 Dimensiones solicitadas: 300.0 x 80.7 pt")
    print("   🔧 Modo: adjust")
    print("   🖼️  Logo: logo-institucion.png")
    print("   ⚙️  Force dimensions: False")
    print("   ⏰ URL expira en: 7200 segundos")
    print("   ✅ Request válido - incluirá logo institucional")
    
    # Ejemplo 4: Compatibilidad con API original
    print("\n📋 Ejemplo 4: Compatibilidad con API original (sin dimensiones pt)")
    
    event4 = {
        "httpMethod": "POST",
        "headers": {
            "x-api-key": "StampGenerator2024Key",
            "x-bucket-name": "test-bucket", 
            "Content-Type": "application/json"
        },
        "body": json.dumps({
            "title": "DOCUMENTO TRADICIONAL",
            "properties": [
                {"label": "Número", "value": "TRAD-2024-004"},
                {"label": "Estado", "value": "PROCESADO"}
            ],
            "qr_content": "https://tradicional.gov.co/doc/004",
            "output_filename": "tradicional-004.png"
        })
    }
    
    print("   📤 Enviando request...")
    print("   📏 Sin dimensiones pt especificadas")
    print("   🔧 Modo: automático (cálculo basado en contenido)")
    print("   ✅ Request válido - mantiene compatibilidad total")
    
    print("\n" + "=" * 70)
    print("📚 RESUMEN DE NUEVAS FUNCIONALIDADES:")
    print("✅ Dimensiones en puntos (pt) con width_pt y height_pt")
    print("✅ Modo 'adjust': ajusta dimensiones manteniendo proporción")
    print("✅ Modo 'diagonal': calcula basado en diagonal de dimensiones")
    print("✅ force_dimensions: controla si se respetan dimensiones exactas")
    print("✅ Proporción 3.715 siempre mantenida")
    print("✅ Compatibilidad total con API original")
    print("✅ Soporte completo para logos")
    print("✅ Respuesta incluye dimensiones finales")
    
    print("\n🎯 CASOS DE USO RECOMENDADOS:")
    print("• Estampillas estándar: 200x53.8 pt (modo adjust)")
    print("• Certificados grandes: 300x80.7 pt (modo adjust)")
    print("• Dimensiones exactas: cualquier tamaño + force_dimensions=true")
    print("• Mantener 'tamaño visual': usar modo diagonal")
    
    return True

if __name__ == "__main__":
    test_api_with_pt_dimensions()
