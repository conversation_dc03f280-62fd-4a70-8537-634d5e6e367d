#!/usr/bin/env python3
"""
Script de prueba para verificar las nuevas funcionalidades de dimensiones en puntos
"""

import sys
import os
import math
from stamp_generator import StampGenerator

def test_pt_dimensions():
    """Prueba las dimensiones especificadas en puntos"""
    
    generator = StampGenerator()
    
    # Casos de prueba con diferentes dimensiones en puntos
    test_cases = [
        {
            "name": "Estampilla estándar (200x53.8 pt)",
            "width_pt": 200.0,
            "height_pt": 53.8,
            "mode": "adjust"
        },
        {
            "name": "Estampilla grande (300x80.7 pt)",
            "width_pt": 300.0,
            "height_pt": 80.7,
            "mode": "adjust"
        },
        {
            "name": "Estampilla diagonal (200x53.8 pt)",
            "width_pt": 200.0,
            "height_pt": 53.8,
            "mode": "diagonal"
        },
        {
            "name": "Estampilla pequeña (100x26.9 pt)",
            "width_pt": 100.0,
            "height_pt": 26.9,
            "mode": "adjust"
        },
        {
            "name": "Dimensiones no proporcionales (150x100 pt) - adjust",
            "width_pt": 150.0,
            "height_pt": 100.0,
            "mode": "adjust"
        },
        {
            "name": "Dimensiones no proporcionales (150x100 pt) - diagonal",
            "width_pt": 150.0,
            "height_pt": 100.0,
            "mode": "diagonal"
        }
    ]
    
    print("🧪 Probando dimensiones en puntos...")
    print("=" * 80)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print(f"   📏 Input: {test_case['width_pt']} x {test_case['height_pt']} pt")
        print(f"   🔧 Modo: {test_case['mode']}")
        
        # Generar estampilla (forzando dimensiones para ver el resultado exacto)
        stamp_io = generator.generate_stamp(
            title="DOC",  # Título más corto para que quepa en dimensiones pequeñas
            properties={
                "ID": f"{i:03d}",
                "OK": "SI"
            },
            qr_content=f"test{i}",
            logo_image=None,
            transparent_background=False,
            width_pt=test_case["width_pt"],
            height_pt=test_case["height_pt"],
            dimension_mode=test_case["mode"],
            force_dimensions=True
        )
        
        # Leer dimensiones de la imagen generada
        from PIL import Image
        stamp_io.seek(0)
        image = Image.open(stamp_io)
        width_px, height_px = image.size
        
        # Calcular proporción y conversiones
        aspect_ratio = width_px / height_px
        expected_ratio = 3.715
        ratio_diff = abs(aspect_ratio - expected_ratio)
        
        # Convertir píxeles de vuelta a puntos
        width_pt_result = width_px * 3/4  # px to pt conversion
        height_pt_result = height_px * 3/4
        
        # Mostrar resultados
        print(f"   📐 Resultado: {width_px} x {height_px} px")
        print(f"   📐 En puntos: {width_pt_result:.1f} x {height_pt_result:.1f} pt")
        print(f"   📊 Proporción: {aspect_ratio:.3f}")
        print(f"   🎯 Esperada: {expected_ratio}")
        print(f"   📏 Diferencia: {ratio_diff:.6f}")
        
        # Verificar que la proporción sea correcta (tolerancia de 0.02 para números enteros)
        if ratio_diff < 0.02:
            print(f"   ✅ CORRECTO - Proporción mantenida")
        else:
            print(f"   ❌ ERROR - Proporción incorrecta")
            return False
        
        # Calcular y mostrar información adicional según el modo
        if test_case["mode"] == "diagonal":
            input_diagonal = math.sqrt(test_case["width_pt"]**2 + test_case["height_pt"]**2)
            output_diagonal = math.sqrt(width_pt_result**2 + height_pt_result**2)
            print(f"   📐 Diagonal input: {input_diagonal:.1f} pt")
            print(f"   📐 Diagonal output: {output_diagonal:.1f} pt")
            print(f"   📏 Diferencia diagonal: {abs(input_diagonal - output_diagonal):.1f} pt")
        
        # Guardar imagen de prueba
        output_filename = f"test_pt_{i}_{test_case['mode']}.png"
        with open(output_filename, 'wb') as f:
            stamp_io.seek(0)
            f.write(stamp_io.read())
        print(f"   💾 Guardado: {output_filename}")
    
    print("\n" + "=" * 80)
    print("🎉 ¡Todas las pruebas de dimensiones en puntos pasaron!")
    return True

def test_conversion_functions():
    """Prueba las funciones de conversión"""
    
    generator = StampGenerator()
    
    print("\n🔍 Probando funciones de conversión...")
    print("=" * 80)
    
    # Prueba conversión pt a px
    test_pt_values = [100, 200, 300, 53.8, 80.7]
    
    for pt_val in test_pt_values:
        px_val = generator._convert_pt_to_px(pt_val)
        pt_back = px_val * 3/4
        print(f"   {pt_val} pt → {px_val} px → {pt_back:.1f} pt")
    
    # Prueba cálculo diagonal
    print(f"\n   Prueba cálculo diagonal:")
    width_pt, height_pt = 200.0, 53.8
    width_px, height_px = generator._calculate_dimensions_from_diagonal(width_pt, height_pt)
    
    input_diagonal = math.sqrt(width_pt**2 + height_pt**2)
    output_diagonal_pt = math.sqrt((width_px * 3/4)**2 + (height_px * 3/4)**2)
    
    print(f"   Input: {width_pt} x {height_pt} pt (diagonal: {input_diagonal:.1f} pt)")
    print(f"   Output: {width_px} x {height_px} px")
    print(f"   Output en pt: {width_px * 3/4:.1f} x {height_px * 3/4:.1f} pt")
    print(f"   Diagonal output: {output_diagonal_pt:.1f} pt")
    print(f"   Proporción: {width_px/height_px:.3f}")
    
    print("✅ Funciones de conversión verificadas")
    return True

if __name__ == "__main__":
    print("🚀 Iniciando pruebas de dimensiones en puntos")
    
    try:
        # Ejecutar pruebas
        test1_passed = test_pt_dimensions()
        test2_passed = test_conversion_functions()
        
        if test1_passed and test2_passed:
            print("\n🎊 ¡TODAS LAS PRUEBAS EXITOSAS!")
            print("La lambda maneja correctamente las dimensiones en puntos")
            sys.exit(0)
        else:
            print("\n💥 ALGUNAS PRUEBAS FALLARON")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 ERROR durante las pruebas: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
