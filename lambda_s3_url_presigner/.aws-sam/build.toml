# This file is auto generated by SAM CLI build command

[function_build_definitions.53d763fa-209c-465d-88ec-ef69a1d54310]
packagetype = "Image"
functions = ["S3PresignedUrlFunction"]

[function_build_definitions.53d763fa-209c-465d-88ec-ef69a1d54310.metadata]
Dockerfile = "Dockerfile"
DockerContext = "/Users/<USER>/github/Sucomunicacion/gedsys-tools/gedsystool-lambdas/lambda_s3_url_presigner"
DockerTag = "python3.11-v1"

[layer_build_definitions]
