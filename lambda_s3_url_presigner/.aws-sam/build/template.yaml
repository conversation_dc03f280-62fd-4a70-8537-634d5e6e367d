AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: S3 Presigned URL Generator Lambda - Local Development
Globals:
  Function:
    Timeout: 30
    MemorySize: 256
Resources:
  S3PresignedUrlFunction:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      Architectures:
      - x86_64
      Environment:
        Variables:
          S3_ENDPOINT_URL: http://localhost:9000
          S3_ACCESS_KEY: minioadmin
          S3_SECRET_KEY: minioadmin
          S3_REGION: us-east-1
          API_KEY: bPPYonPCqza4KXdKJVNS
      Events:
        Api:
          Type: Api
          Properties:
            Path: /presign
            Method: post
        HealthCheck:
          Type: Api
          Properties:
            Path: /health
            Method: get
      ImageUri: s3presignedurlfunction:python3.11-v1
    Metadata:
      DockerContext: /Users/<USER>/github/Sucomunicacion/gedsys-tools/gedsystool-lambdas/lambda_s3_url_presigner
      DockerTag: python3.11-v1
      Dockerfile: Dockerfile
      SamResourceId: S3PresignedUrlFunction
Outputs:
  S3PresignedUrlApi:
    Description: API Gateway endpoint URL for S3 Presigned URL function
    Value:
      Fn::Sub: https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/presign/
  S3PresignedUrlFunction:
    Description: S3 Presigned URL Lambda Function ARN
    Value:
      Fn::GetAtt:
      - S3PresignedUrlFunction
      - Arn
